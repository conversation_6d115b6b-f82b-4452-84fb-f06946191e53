'use client'

import React, { useState } from 'react';
import { useTranslations } from 'next-intl';
import { useAllergens } from '@/lib/fetch/item';
import { AllergenDocument } from '@/model/allergen';
import Loading from '@/app/components/Loading';
import ErrorMessage from '@/app/components/ErrorMessage';
import Heading from '@/app/components/Heading';
import Button from '@/app/components/Button';
import { Plus, Edit, Save, X, AlertTriangle } from 'lucide-react';

const AllergensPage = () => {
    const [editingAllergen, setEditingAllergen] = useState<AllergenDocument | null>(null);
    const [isCreating, setIsCreating] = useState(false);
    const [formData, setFormData] = useState({
        id: '',
        name: '',
        description: ''
    });

    const t = useTranslations();
    const { data: allergens, error, isFetching, refetch } = useAllergens();

    const handleEdit = (allergen: AllergenDocument) => {
        setEditingAllergen(allergen);
        setFormData({
            id: allergen.id,
            name: allergen.name,
            description: allergen.description
        });
    };

    const handleCreate = () => {
        setIsCreating(true);
        setFormData({
            id: '',
            name: '',
            description: ''
        });
    };

    const handleSave = async () => {
        try {
            const response = await fetch('/api/allergens', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(formData)
            });

            if (response.ok) {
                refetch();
                setEditingAllergen(null);
                setIsCreating(false);
            } else {
                console.error('Failed to save allergen');
            }
        } catch (error) {
            console.error('Error saving allergen:', error);
        }
    };

    const handleCancel = () => {
        setEditingAllergen(null);
        setIsCreating(false);
        setFormData({
            id: '',
            name: '',
            description: ''
        });
    };

    if (isFetching) {
        return <Loading message="Loading allergens..." />;
    }

    if (error) {
        return <ErrorMessage error={error.message} />;
    }

    return (
        <div className="space-y-6">
            <Heading 
                title="Manage Allergens" 
                description="Define allergens for menu items"
                icon={<AlertTriangle className="w-10 h-10 text-gray-900" />}
            />

            {/* Create New Allergen */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <div className="flex items-center justify-between mb-4">
                    <h2 className="text-lg font-semibold">Allergens</h2>
                    <Button
                        onClick={handleCreate}
                        className="bg-primary-600 hover:bg-primary-700 text-white"
                    >
                        <Plus className="w-4 h-4 mr-2" />
                        Add Allergen
                    </Button>
                </div>

                {/* Create/Edit Form */}
                {(isCreating || editingAllergen) && (
                    <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                        <h3 className="font-medium mb-4">
                            {isCreating ? 'Create New Allergen' : 'Edit Allergen'}
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    ID
                                </label>
                                <input
                                    type="text"
                                    value={formData.id}
                                    onChange={(e) => setFormData({ ...formData, id: e.target.value })}
                                    className="w-full p-2 border border-gray-300 rounded-md"
                                    placeholder="1, 2, 3..."
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Name
                                </label>
                                <input
                                    type="text"
                                    value={formData.name}
                                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                                    className="w-full p-2 border border-gray-300 rounded-md"
                                    placeholder="Allergen Name"
                                />
                            </div>
                            <div className="md:col-span-2">
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Description
                                </label>
                                <textarea
                                    value={formData.description}
                                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                                    className="w-full p-2 border border-gray-300 rounded-md"
                                    rows={3}
                                    placeholder="Detailed description of the allergen"
                                />
                            </div>
                        </div>
                        <div className="flex gap-2 mt-4">
                            <Button
                                onClick={handleSave}
                                className="bg-green-600 hover:bg-green-700 text-white"
                            >
                                <Save className="w-4 h-4 mr-2" />
                                Save
                            </Button>
                            <Button
                                onClick={handleCancel}
                                className="bg-gray-500 hover:bg-gray-600 text-white"
                            >
                                <X className="w-4 h-4 mr-2" />
                                Cancel
                            </Button>
                        </div>
                    </div>
                )}

                {/* Allergens List */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {allergens?.map((allergen) => (
                        <div
                            key={allergen._id.toString()}
                            className="p-4 border border-gray-200 rounded-lg"
                        >
                            <div className="flex items-start justify-between mb-2">
                                <div className="flex items-center gap-2">
                                    <span className="font-medium text-lg">{allergen.name}</span>
                                    <span className="text-sm bg-red-100 text-red-800 px-2 py-1 rounded">
                                        #{allergen.id}
                                    </span>
                                </div>
                                <Button
                                    onClick={() => handleEdit(allergen)}
                                    className="bg-blue-500 hover:bg-blue-600 text-white"
                                >
                                    <Edit className="w-4 h-4" />
                                </Button>
                            </div>
                            <p className="text-sm text-gray-600">{allergen.description}</p>
                        </div>
                    ))}
                </div>

                {(!allergens || allergens.length === 0) && (
                    <div className="text-center py-8">
                        <AlertTriangle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-500">No allergens defined yet.</p>
                        <p className="text-sm text-gray-400">Click "Add Allergen" to get started.</p>
                    </div>
                )}
            </div>
        </div>
    );
};

export default AllergensPage;
