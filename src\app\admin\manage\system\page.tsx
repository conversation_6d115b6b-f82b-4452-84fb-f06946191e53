'use client'

import React, { useState } from 'react';
import { useTranslations } from 'next-intl';
import Loading from '@/app/components/Loading';
import Heading from '@/app/components/Heading';
import Button from '@/app/components/Button';
import { Database, RefreshCw, Upload, Download, AlertCircle, CheckCircle } from 'lucide-react';

const SystemManagePage = () => {
    const [isSeeding, setIsSeeding] = useState(false);
    const [isMigrating, setIsMigrating] = useState(false);
    const [seedResult, setSeedResult] = useState<string | null>(null);
    const [migrateResult, setMigrateResult] = useState<string | null>(null);

    const t = useTranslations();

    const handleSeedData = async () => {
        setIsSeeding(true);
        setSeedResult(null);
        
        try {
            const response = await fetch('/api/seed', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });
            
            const result = await response.json();
            
            if (response.ok) {
                setSeedResult(`✅ ${result.message}`);
            } else {
                setSeedResult(`❌ Error: ${result.message}`);
            }
        } catch (error) {
            setSeedResult(`❌ Error: ${error.message}`);
        } finally {
            setIsSeeding(false);
        }
    };

    const handleMigrateData = async () => {
        setIsMigrating(true);
        setMigrateResult(null);
        
        try {
            const response = await fetch('/api/migrate', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });
            
            const result = await response.json();
            
            if (response.ok) {
                setMigrateResult(`✅ ${result.message} (${result.migratedItems} items migrated)`);
            } else {
                setMigrateResult(`❌ Error: ${result.message}`);
            }
        } catch (error) {
            setMigrateResult(`❌ Error: ${error.message}`);
        } finally {
            setIsMigrating(false);
        }
    };

    return (
        <div className="space-y-6">
            <Heading 
                title="System Management" 
                description="Manage system data, migrations, and configurations"
                icon={<Database className="w-10 h-10 text-gray-900" />}
            />

            {/* Data Management */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <h2 className="text-lg font-semibold mb-4">Data Management</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Seed Data */}
                    <div className="p-4 border border-gray-200 rounded-lg">
                        <div className="flex items-center gap-3 mb-3">
                            <Upload className="w-5 h-5 text-blue-600" />
                            <h3 className="font-medium">Seed Initial Data</h3>
                        </div>
                        <p className="text-sm text-gray-600 mb-4">
                            Populate the database with categories and allergens based on Quick Pizza standards.
                        </p>
                        <Button
                            onClick={handleSeedData}
                            disabled={isSeeding}
                            className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                        >
                            {isSeeding ? (
                                <>
                                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                                    Seeding...
                                </>
                            ) : (
                                <>
                                    <Upload className="w-4 h-4 mr-2" />
                                    Seed Data
                                </>
                            )}
                        </Button>
                        {seedResult && (
                            <div className="mt-3 p-3 bg-gray-50 rounded text-sm">
                                {seedResult}
                            </div>
                        )}
                    </div>

                    {/* Migrate Existing Data */}
                    <div className="p-4 border border-gray-200 rounded-lg">
                        <div className="flex items-center gap-3 mb-3">
                            <RefreshCw className="w-5 h-5 text-green-600" />
                            <h3 className="font-medium">Migrate Existing Items</h3>
                        </div>
                        <p className="text-sm text-gray-600 mb-4">
                            Convert existing menu items to the new enhanced format with categories, allergens, and dietary information.
                        </p>
                        <Button
                            onClick={handleMigrateData}
                            disabled={isMigrating}
                            className="w-full bg-green-600 hover:bg-green-700 text-white"
                        >
                            {isMigrating ? (
                                <>
                                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                                    Migrating...
                                </>
                            ) : (
                                <>
                                    <RefreshCw className="w-4 h-4 mr-2" />
                                    Migrate Data
                                </>
                            )}
                        </Button>
                        {migrateResult && (
                            <div className="mt-3 p-3 bg-gray-50 rounded text-sm">
                                {migrateResult}
                            </div>
                        )}
                    </div>
                </div>
            </div>

            {/* System Information */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <h2 className="text-lg font-semibold mb-4">System Information</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="p-4 bg-blue-50 rounded-lg">
                        <div className="flex items-center gap-2 mb-2">
                            <CheckCircle className="w-5 h-5 text-blue-600" />
                            <h3 className="font-medium text-blue-900">Enhanced Menu System</h3>
                        </div>
                        <p className="text-sm text-blue-700">
                            Advanced categorization, allergen tracking, and customization features
                        </p>
                    </div>
                    
                    <div className="p-4 bg-green-50 rounded-lg">
                        <div className="flex items-center gap-2 mb-2">
                            <CheckCircle className="w-5 h-5 text-green-600" />
                            <h3 className="font-medium text-green-900">Pizza Customization</h3>
                        </div>
                        <p className="text-sm text-green-700">
                            Size options, toppings, and ingredient modifications
                        </p>
                    </div>
                    
                    <div className="p-4 bg-purple-50 rounded-lg">
                        <div className="flex items-center gap-2 mb-2">
                            <CheckCircle className="w-5 h-5 text-purple-600" />
                            <h3 className="font-medium text-purple-900">Quick Pizza Standard</h3>
                        </div>
                        <p className="text-sm text-purple-700">
                            Matches competitor's advanced menu structure
                        </p>
                    </div>
                </div>
            </div>

            {/* Migration Instructions */}
            <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-6">
                <div className="flex items-start gap-3">
                    <AlertCircle className="w-5 h-5 text-yellow-600 mt-0.5" />
                    <div>
                        <h3 className="font-medium text-yellow-900 mb-2">Migration Instructions</h3>
                        <ol className="text-sm text-yellow-800 space-y-1 list-decimal list-inside">
                            <li>First, run "Seed Data" to create categories and allergens</li>
                            <li>Then, run "Migrate Data" to convert existing menu items</li>
                            <li>Review the migrated items in the menu management section</li>
                            <li>Adjust categories, allergens, and customization options as needed</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default SystemManagePage;
